<?php

use App\Http\Controllers;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\UW\ReportController as UWReportController;
use App\Http\Controllers\UW\StoreController as UWStoreController;
use App\Http\Controllers\UW\UserController as UWUserController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('blacklist')->group(function () {
    Route::get('/api-docs', [Controllers\ApiDocController::class, 'adminApiDocs'])->name('api-docs-admin');
    Route::get('/api-docs-postman', [Controllers\ApiDocController::class, 'downloadAdminPostmanJson']);

    Route::middleware('dropdown.list')->group(function () {
        Route::post('/login', [Controllers\AuthController::class, 'adminLogin']);
    });

    Route::controller(Controllers\CountryController::class)->prefix('country')->group(function () {
        Route::post('/get', 'getCountry')->middleware('dropdown.list');
    });

    Route::middleware('auth:admin', 'jwt.reset', 'dropdown.list')->group(function () {
        // this is for FE to get dropdown only
        Route::post('/empty', [Controllers\DeveloperController::class, 'empty']);

        // Auth
        Route::post('/logout', [Controllers\AuthController::class, 'adminLogout'])->withoutMiddleware(['jwt.reset', 'force.reset', 'dropdown.list']);
        Route::post('/my-profile', [Controllers\AdminController::class, 'myProfile']);
        Route::post('/change-pwd', [Controllers\AuthController::class, 'adminChangePassword']);

        // Admin
        Route::controller(Controllers\AdminController::class)->prefix('admin')->group(function () {
            Route::post('/dashboard', 'dashboard')->middleware('can:admin/dashboard');
            Route::post('/list', 'get')->middleware('can:admin/list');
            Route::post('/create', 'create')->middleware('can:admin/create');
            Route::post('/detail', 'detail')->middleware('can:admin/detail');
            Route::post('/edit', 'edit')->middleware('can:admin/edit');
            Route::post('/get-role-list', 'getRoleList')->middleware('can:admin/get-role-list');
            Route::post('/add-role', 'addRole')->middleware('can:admin/add-role');
            Route::post('/get-role-detail', 'getRoleDetail')->middleware('can:admin/get-role-detail');
            Route::post('/edit-role-detail', 'editRoleDetail')->middleware('can:admin/edit-role-detail');

            Route::post('/get-device-list', 'getDeviceList')->middleware('can:admin/get-device-list');
            Route::post('/edit-device', 'editDevice')->middleware('can:admin/edit-device');
        });

        // Credit
        Route::controller(Controllers\CreditController::class)->prefix('credit')->group(function () {
            Route::post('/user-wallet-detail', 'getUserWalletDetail')->middleware('can:credit/user-wallet-detail');
            Route::post('/adjustment', 'adjustment')->middleware('can:credit/adjustment');
            Route::post('/transaction-list', 'getTransactionList')->middleware('can:credit/transaction-list');
            Route::post('/transfer-transaction-list', 'allTransferTransactionList')->middleware('can:credit/transfer-transaction-list');
            Route::post('/myr-transfer-transaction-list', 'allTransferTransactionList')->middleware('can:credit/myr-transfer-transaction-list');
            Route::post('/usdt-transfer-transaction-list', 'allTransferTransactionList')->middleware('can:credit/usdt-transfer-transaction-list');
            Route::post('/thb-transfer-transaction-list', 'allTransferTransactionList')->middleware('can:credit/thb-transfer-transaction-list');
            Route::post('/all-transaction-list', 'allTransactionList')->middleware('can:credit/all-transaction-list');
            Route::post('/myr-transaction-list', 'allTransactionList')->middleware('can:credit/myr-transaction-list');
            Route::post('/usdt-transaction-list', 'allTransactionList')->middleware('can:credit/usdt-transaction-list');
            Route::post('/thb-transaction-list', 'allTransactionList')->middleware('can:credit/thb-transaction-list');
            Route::post('/balance-list', 'balanceList')->middleware('can:credit/balance-list');
            Route::post('/transfer', 'transferConfirmation')->middleware('can:credit/transfer');
            Route::post('/transfer-list', 'getTransferList')->middleware('can:credit/transfer-list');
            Route::post('/get-deposit-list', 'getDepositList')->middleware('can:credit/get-deposit-list');
            Route::post('/get-fpay-deposit-list', 'getFPayDepositList')->middleware('can:credit/get-fpay-deposit-list');
            Route::post('/get-fpay-telco-deposit-list', 'getFPayTelcoDepositList')->middleware('can:credit/get-fpay-telco-deposit-list');
            Route::post('/get-ewallet-deposit-list', 'getEwalletDepositList')->middleware('can:credit/get-ewallet-deposit-list');
            Route::post('/get-thb-ewallet-deposit-list', 'getThbEwalletDepositList')->middleware('can:credit/get-thb-ewallet-deposit-list');
            Route::post('/get-thb-manual-deposit-list', 'getThbManualDepositList')->middleware('can:credit/get-thb-manual-deposit-list');
            Route::post('/get-usdt-deposit-list', 'getUsdtEwalletDepositList')->middleware('can:credit/get-usdt-deposit-list');
            Route::post('/get-deposit-det', 'getDepositDet')->middleware('can:credit/get-deposit-det');
            Route::post('/update-deposit', 'updateDeposit')->middleware('can:credit/update-deposit');
            Route::post('/withdraw-list', 'getWithdrawalList')->middleware('can:credit/withdraw-list');
            Route::post('/withdraw-det', 'getWithdrawalDet')->middleware('can:credit/withdraw-det');
            Route::post('/withdraw-update', 'withdrawUpdate')->middleware('can:credit/withdraw-update')->middleware('throttle:1,0.03');
            Route::post('/withdrawal-bank-list', 'withdrawalBankList')->middleware('can:credit/withdrawal-bank-list');
            Route::post('/withdrawal-bank-detail', 'withdrawalBankDetail'); // ->middleware('can:credit/withdrawal-bank-detail');
            Route::post('/update-withdrawal-bank', 'updateWithdrawalBankDetail'); // ->middleware('can:credit/withdrawal-bank-detail');
            Route::post('/delete-withdrawal-bank', 'deleteWithdrawalBankDetail'); // ->middleware('can:credit/withdrawal-bank-detail');
            Route::post('/generate-withdrawal-order', 'generateWithdrawalOrder'); // ->middleware('can:credit/generate-withdrawal-order');

            // from tk8/ tk8th
            Route::post('/get-platform-fpay-deposit-list', 'getPlatformFPayDepositList')->middleware('can:credit/get-platform-fpay-deposit-list');
            Route::post('/get-thb-platform-fpay-deposit-list', 'getPlatformFPayDepositList')->middleware('can:credit/get-thb-platform-fpay-deposit-list');

            Route::post('/conversion-list', 'getConversionList')->middleware('can:credit/conversion-list');

            // Deposit Setting
            Route::post('/deposit-method', 'getDepositMethod')->middleware('can:credit/deposit-method');
            Route::post('/set-deposit-method', 'setDepositMethod')->middleware('can:credit/set-deposit-method');
            Route::post('/get-deposit-method-listing', 'getDepositMethodListing')->middleware('can:credit/get-deposit-method-listing');
            Route::post('/create-deposit', 'addDeposit');

            // External transfer refund
            Route::post('/ex-transfer-refund', 'exTransferRefund')->middleware('can:credit/ex-transfer-refund');
            Route::post('/ex-transfer-recall-i8', 'exTransferRecallOC')->middleware('can:credit/ex-transfer-recall-i8');

            // External transfer credit
            Route::post('/ex-transfer-credit', 'exTransferCredit')->middleware('can:credit/ex-transfer-credit');

            // Withdrawal Third Party
            Route::post('/withdraw-third-party-list', 'getWithdrawalThirdPartyList')->middleware('can:credit/withdraw-third-party-list');
            Route::post('/withdraw-third-party-det', 'getWithdrawalThirdPartyDet')->middleware('can:credit/withdraw-third-party-det');
            Route::post('/withdraw-third-party-update', 'withdrawThirdPartyUpdate')->middleware('can:credit/withdraw-third-party-update');
        });

        // User
        Route::controller(Controllers\UserController::class)->prefix('member')->group(function () {
            Route::post('/list', 'get')->middleware('can:member/list');
            Route::post('/search', 'search')->middleware('can:member/list');
            Route::post('/search-store', 'searchStore')->middleware('can:member/list');
            Route::post('/register', 'register')->middleware('can:member/register');
            Route::post('/detail', 'detail')->middleware('can:member/detail');
            Route::post('/edit', 'edit')->middleware('can:member/edit');
            Route::post('/change-user-pwd', 'changePassword')->middleware('can:member/change-user-pwd');
            Route::post('/reset-user-pwd', 'adminResetPassword')->middleware('can:member/reset-user-pwd');
            Route::post('/change-user-transaction-pwd', 'changeTransactionPassword')->middleware('can:member/change-user-transaction-pwd');
            Route::post('/reset-user-transaction-pwd', 'adminResetTransactionPassword')->middleware('can:member/reset-user-transaction-pwd');
            Route::post('/login-to-member', 'loginToMember')->middleware('can:member/login-to-member');
            Route::post('/user-primary-bank', 'getUserPrimaryBank')->middleware('can:member/user-primary-bank');
            Route::post('/user-default-address', 'getUserDefaultAddress')->middleware('can:member/user-default-address');
            Route::post('/update-user-primary-bank', 'updateUserPrimaryBank')->middleware('can:member/update-user-primary-bank');
            Route::post('/update-user-default-address', 'updateUserDefaultAddress')->middleware('can:member/update-user-default-address');
        });

        // Tree
        Route::controller(Controllers\TreeController::class)->prefix('tree')->group(function () {
            Route::post('/get-tree-sponsor', 'getTreeSponsor')->middleware('can:tree/get-tree-sponsor');
            Route::post('/get-tree-sponsor-vertical', 'getTreeSponsorVertical')->middleware('can:tree/get-tree-sponsor-vertical');
            Route::post('/change-referral', 'changeReferral')->middleware('can:tree/change-referral');
        });

        // Fingerprint
        Route::controller(Controllers\FingerprintController::class)->prefix('fingerprint')->group(function () {
            Route::post('/list', 'get')->middleware('can:fingerprint/list');
        });

        // Currency
        Route::controller(Controllers\CurrencyController::class)->prefix('currency')->group(function () {
            Route::post('/preset', 'preset');
            Route::post('/edit', 'edit')->middleware('can:currency/edit');
            Route::post('/batch-edit', 'batchEdit')->middleware('can:currency/batch-edit');
            Route::post('/rate-history', 'getRateHistory')->middleware('can:currency/rate-history');
            Route::post('/withdrawal-preset', 'withdrawalCurrencyPreset')->middleware('can:currency/withdrawal-preset');
            Route::post('/withdrawal-edit', 'withdrawalCurrencyEdit')->middleware('can:currency/withdrawal-edit');
            Route::post('/batch-withdrawal-edit', 'batchWithdrawalCurrencyEdit')->middleware('can:currency/batch-withdrawal-edit');

            Route::post('/conversion-preset', 'conversionCurrencyPreset')->middleware('can:currency/conversion-preset');
            Route::post('/conversion-edit', 'conversionCurrencyEdit')->middleware('can:currency/conversion-edit');
            Route::post('/batch-conversion-edit', 'batchconversionCurrencyEdit')->middleware('can:currency/batch-conversion-edit');
        });

        // Permission
        Route::controller(Controllers\PermissionController::class)->prefix('permission')->group(function () {
            Route::post('/list', 'get')->middleware('can:permission/list');
        });

        // Activity Log
        Route::controller(Controllers\ActivityLogController::class)->prefix('activity-log')->group(function () {
            Route::post('/list', 'get')->middleware('can:activity-log/list');
        });

        // File
        Route::controller(Controllers\FileController::class)->group(function () {
            Route::post('/upload-s3', 'uploadS3');
            Route::post('/upload-s3-multi', 'uploadS3Multi');
        });

        // KYC
        Route::controller(Controllers\KycController::class)->prefix('kyc')->group(function () {
            Route::post('/get-user-kyc', 'getUserKyc')->middleware('can:kyc/get-user-kyc');
            Route::post('/get-user-kyc-det', 'getUserKycDet')->middleware('can:kyc/get-user-kyc-det');
            Route::post('/update-kyc', 'updateKyc')->middleware('can:kyc/update-kyc');
        });

        // Product
        Route::controller(Controllers\ProductController::class)->prefix('product')->group(function () {
            Route::post('/ex-transfer-list', 'getExTransferList')->middleware('can:product/ex-transfer-list');
        });

        // Service
        Route::controller(Controllers\ServiceController::class)->prefix('service')->group(function () {
            Route::post('/add-service', 'addService')->middleware('can:service/add-service');
            Route::post('/get-service-list', 'getServiceList')->middleware('can:service/get-service-list');
            Route::post('/get-service-detail', 'getServiceDetail')->middleware('can:service/get-service-detail');
            Route::post('/edit-service', 'editService')->middleware('can:service/edit-service');
            Route::post('/arrange-service', 'arrangeService')->middleware('can:service/arrange-service');
        });

        // Machine
        Route::controller(Controllers\MachineController::class)->prefix('machine')->group(function () {
            Route::post('/add-machine', 'addMachine');
            Route::post('/get-machine-list', 'getMachineList');
            Route::post('/get-machine-detail', 'getMachineDetail');
            Route::post('/edit-machine', 'editMachine');
            Route::post('/delete-machine', 'deleteMachine');
        });

        // Bank
        Route::controller(Controllers\BankController::class)->prefix('bank')->group(function () {
            Route::post('/add-bank', 'addBank')->middleware('can:bank/add-bank');
            Route::post('/get-bank-list', 'getBankList')->middleware('can:bank/get-bank-list');
            Route::post('/get-bank-detail', 'getBankDetail')->middleware('can:bank/get-bank-detail');
            Route::post('/edit-bank', 'editBank')->middleware('can:bank/edit-bank');
        });

        // Report
        Route::controller(Controllers\ReportController::class)->prefix('report')->group(function () {
            Route::post('/otp-sent-history', 'getOTPSentReport')->middleware('can:report/otp-sent-history');
            Route::post('/export-report', 'getExportReport')->middleware('can:report/export-report');
            Route::post('/download-export-report', 'downloadExportReport')->middleware('can:report/download-export-report');
            Route::post('/sales-summary-report', 'getSalesSummaryReport')->middleware('can:report/sales-summary-report');
            Route::post('/machine-summary-report', 'getMachineSummaryReport')/* ->middleware('can:report/sales-summary-report') */;
            Route::post('/machine-summary-report-group-by-machine', 'getMachineSummaryReportGroupByMachineId')/* ->middleware('can:report/sales-summary-report') */;
            Route::post('/wallet-balance-report', 'getWalletBalanceReport')->middleware('can:report/wallet-balance-report');
            Route::post('/get-api-deposit-list', 'getAPIDepositReport')->middleware('can:report/get-api-deposit-list');
            Route::post('/get-api-withdrawal-list', 'getAPIWithdrawalReport')->middleware('can:report/get-api-withdrawal-list');

            Route::post('/get-pos-status', 'getPosStatus')->middleware('can:report/pos-report-record');
            Route::post('/get-pos-report-record', 'getPosReport')->middleware('can:report/pos-report-record');
            Route::post('/get-pos-report-reload', 'getPosReport')->middleware('can:report/pos-report-reload');
            Route::post('/get-pos-report-withdraw', 'getPosReport')->middleware('can:report/pos-report-withdraw');
            Route::post('/machine-summary', 'getMachineSummary');
            Route::post('/get-pos-currency-detail', 'getCurrencyDetail'); // ->middleware('can:currency/get-currency-detail');
            Route::post('/get-summary-profit-loss-report', 'getSummaryProfitLossReport');
            Route::post('/get-store-summary-profit-loss-report', 'getStoreSummaryProfitLossReport');
            Route::post('get-summary-store-report', 'getSummaryStoreReport');
        });

        // Announcement Bar
        Route::controller(Controllers\AnnouncementBarController::class)->prefix('announcement-bar')->group(function () {
            Route::post('/list', 'getAnnouncementBarList')->middleware('can:announcement-bar/list');
            Route::post('/get', 'getAnnouncementBarDetail')->middleware('can:announcement-bar/get');
            Route::post('/add', 'addAnnouncementBar')->middleware('can:announcement-bar/add');
            Route::post('/edit', 'editAnnouncementBar')->middleware('can:announcement-bar/edit');
        });

        // Announcement
        Route::controller(Controllers\AnnouncementController::class)->prefix('announcement')->group(function () {
            Route::post('/announcement-list', 'getAnnouncementList')->middleware('can:announcement/announcement-list');
            Route::post('/announcement-detail', 'getAnnouncementDetail')->middleware('can:announcement/announcement-detail');
            Route::post('/add-announcement', 'addAnnouncement')->middleware('can:announcement/add-announcement');
            Route::post('/edit-announcement', 'editAnnouncement')->middleware('can:announcement/edit-announcement');
            Route::post('/delete-announcement', 'deleteAnnouncement')->middleware('can:announcement/delete-announcement');
        });

        // Banner
        Route::controller(Controllers\BannerController::class)->prefix('banner')->group(function () {
            Route::post('/banner-list', 'getBannerList')->middleware('can:banner/banner-list');
            Route::post('/banner-detail', 'getBannerDetail')->middleware('can:banner/banner-detail');
            Route::post('/add-banner', 'addBanner')->middleware('can:banner/add-banner');
            Route::post('/edit-banner', 'editBanner')->middleware('can:banner/edit-banner');
        });

        // Memo
        Route::controller(Controllers\MemoController::class)->prefix('memo')->group(function () {
            Route::post('/memo-list', 'getMemoList')->middleware('can:memo/memo-list');
            Route::post('/memo-detail', 'getMemoDetail')->middleware('can:memo/memo-detail');
            Route::post('/add-memo', 'addMemo')->middleware('can:memo/add-memo');
            Route::post('/edit-memo', 'editMemo')->middleware('can:memo/edit-memo');
            Route::post('/delete-memo', 'deleteMemo')->middleware('can:memo/delete-memo');
        });

        // Document
        Route::controller(Controllers\DocumentController::class)->prefix('document')->group(function () {
            Route::post('/list', 'getDocumentList')->middleware('can:document/list');
            Route::post('/get', 'getDocumentDetail')->middleware('can:document/get');
            Route::post('/add', 'addDocument')->middleware('can:document/add');
            Route::post('/edit', 'editDocument')->middleware('can:document/edit');
        });

        // Telex Transfer
        Route::controller(Controllers\TTransferController::class)->prefix('tt-transfer')->group(function () {
            Route::post('/get-telex-transfer-list', 'getTelexTransferList')->middleware('can:tt-transfer/get-telex-transfer-list');
            Route::post('/get-telex-transfer-detail', 'getTelexTransferDetail')->middleware('can:tt-transfer/get-telex-transfer-detail');
            Route::post('/edit-telex-transfer', 'editTelexTransfer')->middleware('can:tt-transfer/edit-telex-transfer');
        });

        // Schedule Module
        Route::controller(Controllers\ScheduleController::class)->prefix('schedule')->group(function () {
            Route::post('/get-schedule-api', 'getScheduleAPI')->middleware('can:schedule/get-schedule-api');
            Route::post('/add-schedule', 'addSchedule')->middleware('can:schedule/add-schedule');
            Route::post('/get-schedule-list', 'getScheduleList')->middleware('can:schedule/get-schedule-list');
            Route::post('/edit-schedule', 'editSchedule')->middleware('can:schedule/edit-schedule');
        });

        // Card
        Route::controller(Controllers\CardController::class)->prefix('card')->group(function () {
            Route::post('/card-list', 'getCardList')->middleware('can:card/card-list');
            Route::post('/card-detail', 'getCardDetail')->middleware('can:card/card-detail');
            Route::post('/card-transaction', 'getExTransferList')->middleware('can:card/card-transaction');

            Route::post('/card-transaction-list', 'getExTransferList')->middleware('can:card/card-transaction-list');

            Route::post('/store-list', 'getStoreList')->middleware('can:card/store-list');
            Route::post('/store-refresh', 'refreshStore')->middleware('can:card/store-refresh');
        });

        // Game
        Route::controller(Controllers\GameController::class)->prefix('wwj')->group(function () {
            Route::post('/get-account-bet-transaction', 'getAccountBetTransaction');
        });

        // User ticket reward
        Route::controller(Controllers\UserTicketRewardController::class)->prefix('ticket-reward')->group(function () {
            Route::post('/get-ticket-reward', 'getAllUserTicketReward');
            Route::post('/store-ticket-reward', 'storeUserTicketReward');
            Route::post('/update-ticket-reward', 'editUserTicketReward');
            Route::post('/delete-ticket-reward', 'deleteUserTicketReward');
        });

        Route::controller(Controllers\PaymentMethodController::class)->prefix('payment-methods')->group(function () {
            Route::post('/get-payment-methods', 'getPaymentMethods');
            Route::post('/update-payment-method-status', 'updatePaymentMethodStatus');
        });

        // User Level Transaction
        Route::controller(Controllers\UserLevelTransactionController::class)->prefix('user-level-transactions')->group(function () {
            Route::post('/get-rebate-amount-report', 'getRebateAmountReport');
            Route::post('/add-user-level-transaction', 'addUserLevelTransaction');
        });

        Route::prefix('mt')
            ->name('mt.')
            ->group(function () {
                Route::controller(Controllers\MtMachineController::class)
                    ->name('machine.')
                    ->group(function () {
                        Route::post('/get-machine-list', 'getMachineList')->middleware('can:mt/machine-list');
                        Route::post('/get-machine-detail', 'getMechineDetail')->middleware('can:mt/machine-detail')->name('detail');
                        Route::post('/store-machine', 'storeMachine')->middleware('can:mt/store-machine')->name('store');
                        Route::post('/update-machine', 'updateMachine')->middleware('can:mt/update-machine')->name('update');
                        Route::post('/delete-machine', 'deleteMachine')->middleware('can:mt/delete-machine')->name('destory');
                        Route::post('/force-close-id', 'killSwitchByStore');
                        Route::post('/force-close-all', 'killSwitchByAll');
                        Route::post('/force-close-status', 'killSwitchStatus');
                        Route::post('/force-close-id-store', 'killSwitchByStoreOnly');
                        Route::post('/force-machine-refresh', 'forceMachineRefresh');
                        Route::post('/restore-machine-by-store', 'restoreMachineByStore');
                    });

                Route::controller(Controllers\MtUserController::class)
                    ->name('user.')
                    ->group(function () {
                        Route::post('/get-user-list', 'getUserList')->middleware('can:mt/user-list')->name('index');
                        Route::post('/get-user-detail', 'getUserDetail')->middleware('can:mt/user-detail')->name('detail');
                        Route::post('/store-user', 'storeUser')->middleware('can:mt/store-user')->name('store');
                        Route::post('/update-user', 'updateUser')->middleware('can:mt/update-user')->name('update');
                        Route::post('/delete-user', 'deleteUser')->middleware('can:mt/delete-user')->name('destory');
                    });
            });

        // Reward Spin
        Route::controller(Controllers\RewardSpinController::class)->prefix('reward-spin')->group(function () {
            Route::post('/get-lucky-spin-list', 'getLuckySpinList');
            Route::post('/spin-list', 'getSpunList')->middleware('can:reward-spin/spin-list');
            Route::post('/get-user-reward-list', 'getUserRewardList')->middleware('can:reward-spin/get-user-reward-list');
        });

        Route::prefix('notifications')
            ->name('.notification')
            ->controller(NotificationController::class)
            ->group(function () {
                Route::post('/get-notifications', 'getNotifications');
                Route::post('/store-notification', 'storeNotification');
                Route::post('/edit-notification', 'editNotification');
                Route::post('/update-notification', 'updateNotification');
                Route::post('/delete-notification', 'deleteNotification');
                Route::post('/send-notification', 'sendNotification');
            });

        Route::controller(Controllers\UserRebateController::class)->prefix('user-rebate')->group(function () {
            Route::post('/get-user-rebate-list', 'getUserRebateList');
            Route::post('/get-user-rebate-detail', 'userRebateDetail');
            Route::post('/update-rebate-status', 'updateRebateStatus');
        });

        Route::controller(Controllers\TransferController::class)->prefix('p2p-transfer')->group(function () {
            Route::post('/get-transfer-list', 'getAllTransferList');
        });

        Route::controller(Controllers\VipLevelController::class)->prefix('vip-level')->group(function () {
            Route::post('/get-vip-level-list', 'getVipLevelList');
        });

        Route::controller(Controllers\UserLevelController::class)->prefix('user-level')->group(function () {
            Route::post('/get-user-level', 'getUserLevel');
            Route::post('/get-user-level-list', 'getUserLevelList');
            Route::post('/update-user-level', 'updateUserLevel');
        });

        Route::controller(Controllers\AngpaoEventController::class)->prefix('angpao-event')->group(function () {
            Route::post('/list', 'getAngpaoEventList');
            Route::post('/detail', 'getAngpaoEventDetail');
            Route::post('/create', 'createAngpaoEvent');
            Route::post('/update', 'updateAngpaoEvent');
            Route::post('/participants', 'getParticipantsList');
            Route::post('/select-winners', 'selectWinners');
        });
    });

    Route::controller(Controllers\PumpDataController::class)->prefix('pump-data')->group(function () {
        Route::post('/user-data', 'pumpUserData');
        Route::post('/transfer-data', 'pumpTransferData');
        Route::post('/deposit-data', 'pumpDepositData');
        Route::post('/withdraw-data', 'pumpWithdrawData');
        Route::post('/withdraw-approval-data', 'pumpApproveWithdrawData');
    });

    Route::controller(Controllers\UserPromotionController::class)
        ->prefix('promotion')
        ->group(function () {
            Route::post('/get-user-promotion-list', 'getUserPromotionList');
            Route::post('/add-user-promotion', 'addUserPromotion');
            Route::post('/update-user-promotion', 'updateUserPromotion');
            Route::post('/get-user-promotion-summary', 'getUserPromotionSummary');
        });

    Route::controller(Controllers\PromotionController::class)
        ->prefix('promotion')
        ->group(function () {
            Route::post('/create-promotion', 'addPromotion');
            Route::post('/get-promotion-list', 'getPromotions');
            Route::post('/update-promotion', 'updatePromotion');
            Route::post('/edit-promotion', 'editPromotion');
        });

    Route::controller(Controllers\ReportController::class)->prefix('report')->group(function () {
        // Route::post('/get-store-summary-profit-loss-report', 'getStoreSummaryProfitLossReport');
        // Route::post('/get-summary-profit-loss-store', 'getSummaryProfitLossByStoreReport');
        Route::post('get-summary-profit-loss-monthly', 'getSummaryProfitLossMonthlyReport');
    });

    Route::controller(Controllers\GameController::class)->prefix('wwj')->group(function () {
        Route::post('/get-bet-transaction', 'getBetTransaction');
    });

    Route::controller(Controllers\UserTicketRewardController::class)->prefix('ticket')->group(function () {
        Route::post('/get-ticket-reward', 'getAllUserTicketReward');
        Route::post('/import-ticket-reward', 'importUserTicketReward');
        Route::get('/export-ticket-reward', 'exportUserTicketReward');
    });

    Route::controller(Controllers\UserReferralController::class)->prefix('user-referral')->group(function () {
        Route::post('/get-user-referral', 'getUserReferralBonusById');
        Route::post('/get-user-referral-list', 'getUserReferralList');
        Route::post('/approve-user-referral', 'approveUserReferral');
        Route::post('/reject-user-referral', 'rejectUserReferral');
    });

    Route::middleware('whitelist')
        ->group(function () {
            Route::prefix('reports')
                ->name('report.')
                ->controller(UWReportController::class)
                ->group(function () {
                    Route::get('statistics', 'statistics')->name('statistics');
                    Route::get('new-members', 'newMembers')->name('new-members');
                    Route::get('active-users', 'activeUsers')->name('active-users');
                    Route::get('first-time-deposits', 'firstTimeDeposits')->name('first-time-deposits');
                    Route::get('deposit', 'deposit')->name('deposit');
                    Route::get('withdrawal', 'withdrawal')->name('withdrawal');
                    Route::get('bulk-statistics', 'bulkStatistics')->name('bulk-statistics');
                    Route::get('top-players', 'topPlayers')->name('top-players');
                    Route::get('user-referrals', 'userReferrals')->name('user-referrals');
                    Route::get('active-user-statistics', 'getActiveUserStatistics')->name('active-user-statistics');
                    Route::get('event-statistics', 'eventStatistics')->name('event-statistics');
                });

            Route::prefix('stores')
                ->name('store.')
                ->controller(UWStoreController::class)
                ->group(function () {
                    Route::get('all', 'all')->name('all');
                });

            Route::prefix('users')
                ->name('user.')
                ->controller(UWUserController::class)
                ->group(function () {
                    Route::get('all', 'all')->name('all');
                });
        });
});
