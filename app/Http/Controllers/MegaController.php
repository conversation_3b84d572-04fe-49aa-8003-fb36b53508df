<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use <PERSON>\Uuid\Uuid;

class MegaController extends Controller
{
    public function megaCallback(Request $request)
    {
        $sessionId = substr(str_replace(['+', '/', '='], '', base64_encode(random_bytes(32))), 0, 32);
        $id = Uuid::uuid4();
        $result = ['success' => 1, 'sessionId' => $sessionId, 'msg' => 'success'];
        $error = null;

        abort(200, json_encode([
            'id' => $id,
            'result' => $result,
            'error' => $error,
            'jsonrpc' => '2.0',
        ]));
    }
}
