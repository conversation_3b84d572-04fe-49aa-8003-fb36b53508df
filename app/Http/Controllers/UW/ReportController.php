<?php

namespace App\Http\Controllers\UW;

use App\Models\User;
use App\Models\Store;
use App\Models\Deposit;
use App\Models\ExTransfer;
use App\Models\Withdrawal;
use App\Models\UserCardLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\CreditTransaction;
use App\Models\UserPromotion;
use App\Models\UserTurnoverSummary;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use App\Models\Transfer;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Log;

class ReportController extends Controller
{
    public function statistics(Request $request)
    {
        $storeIds = $request->store_ids;
        $startDate = $request->start_date;
        $endDate = $request->end_date;

        $lastMonthStartDate = Carbon::parse($request->start_date)
            ->subMonth()
            ->startOfMonth();

        $lastMonthEndDate = Carbon::parse($request->start_date)
            ->subMonth()
            ->endOfMonth();

        // Total Registrations - keep original implementation
        $totalRegistrations = User::whereIn('store_id', $storeIds)
            ->user()
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->count();

        // Total Active Users - reverting to original logic
        $totalActiveUsers = User::whereHas('deposits', function (Builder $query) use ($startDate, $endDate) {
            $query->where('status', 1)
                ->when($startDate, function ($query) use ($startDate) {
                    return $query->where('approved_at', '>=', $startDate);
                })
                ->when($endDate, function ($query) use ($endDate) {
                    return $query->where('approved_at', '<=', $endDate);
                });
        })
            ->whereIn('store_id', $storeIds)
            ->user()
            ->count();

        // Total Active Users Last Month - updated to use approved_at
        $totalActiveUsersLastMonth = User::whereHas('deposits', function (Builder $query) use ($lastMonthStartDate, $lastMonthEndDate) {
            $query->whereBetween('approved_at', [$lastMonthStartDate, $lastMonthEndDate]);
        })
            ->whereIn('store_id', $storeIds)
            ->user()
            ->active()
            ->count();

        // Query for withdrawal only - separated from turnover to handle missing table
        $withdrawalStats = DB::select("
            SELECT
                COUNT(*) as withdrawal_count,
                COALESCE(SUM(w.amount), 0) as withdrawal_sum
            FROM withdrawal w
            JOIN users u ON w.user_id = u.id
            WHERE u.store_id IN (" . implode(',', $storeIds) . ")
            AND w.status = 1
            " . ($startDate ? "AND w.updated_at >= '$startDate'" : "") . "
            " . ($endDate ? "AND w.updated_at <= '$endDate'" : "") . "
        ")[0];

        // Check if the turnover table exists and get data if it does
        $turnoverStats = (object)[
            'turnover_count' => 0,
            'turnover_sum' => 0
        ];

        try {
            // Only run turnover query if table exists
            if (Schema::hasTable('user_turnover_summary')) {
                $turnoverStatsResult = DB::select("
                    SELECT
                        COALESCE(SUM(uts.bet_count), 0) as turnover_count,
                        COALESCE(SUM(uts.turnover), 0) as turnover_sum
                    FROM user_turnover_summary uts
                    JOIN users u ON uts.user_id = u.id
                    WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                    " . ($startDate ? "AND uts.transaction_at >= '$startDate'" : "") . "
                    " . ($endDate ? "AND uts.transaction_at <= '$endDate'" : "") . "
                ");

                if (count($turnoverStatsResult) > 0) {
                    $turnoverStats = $turnoverStatsResult[0];
                }
            }
        } catch (\Exception $e) {
            // Just log the error and continue with default values
            Log::error('Error querying turnover: ' . $e->getMessage());
        }

        // First-Time Deposits - get regular FTD from deposits within the date range
        // Modified to correctly sum only the first deposit amounts
        $depositFTD = Deposit::selectRaw('COUNT(DISTINCT d.user_id) AS count, SUM(d.amount) AS sum')
            ->from('deposit as d')
            ->join('users as u', 'u.id', '=', 'd.user_id')
            ->whereIn('u.store_id', $storeIds)
            ->where('u.user_type', User::$userType['user-account'])
            ->where('d.status', Deposit::$status['approved'])
            ->whereIn('d.id', function ($subQuery) {
                $subQuery->select(DB::raw('MIN(id)'))
                    ->from('deposit')
                    ->where('status', Deposit::$status['approved'])
                    ->groupBy('user_id');
            })
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('d.approved_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('d.approved_at', '<=', $endDate);
            })
            ->first();

        // Get first-time P2P transfers within the date range
        // Modified to correctly sum only the first P2P transfer amounts
        $p2pFTD = Transfer::selectRaw('COUNT(DISTINCT t.to_id) as count, SUM(t.amount) as sum')
            ->from('transfer as t')
            ->join('users as u', 'u.id', '=', 't.to_id')
            ->whereIn('t.from_id', [1002696, 1002990, 1002702, 1002737, 1002692, 1002790, 1002841, 1003450])
            ->whereIn('u.store_id', $storeIds)
            ->whereIn('t.id', function ($subQuery) {
                $subQuery->select(DB::raw('MIN(id)'))
                    ->from('transfer')
                    ->whereIn('from_id', [1002696, 1002990, 1002702, 1002737, 1002692, 1002790, 1002841, 1003450])
                    ->groupBy('to_id');
            })
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('t.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('t.created_at', '<=', $endDate);
            })
            ->first();

        // Combine the results
        $totalFTD = (object)[
            'count' => $depositFTD->count + $p2pFTD->count,
            'sum' => $depositFTD->sum + $p2pFTD->sum
        ];

        // Get total deposits
        $totalDeposits = Deposit::whereHas('user', function (Builder $query) use ($storeIds) {
            $query->whereIn('store_id', $storeIds)
                ->user();
        })
            ->select(DB::raw('COUNT(*) as count, SUM(amount) AS sum'))
            ->approved()
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('approved_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('approved_at', '<=', $endDate);
            })
            ->first();

        // Second-Time Deposits using the improved approach
        $totalSTD = Deposit::selectRaw('COUNT(DISTINCT second_deposits.user_id) AS count, SUM(second_deposits.amount) AS sum')
            ->from(DB::raw('deposit as second_deposits'))
            ->join(DB::raw('(
                SELECT d1.user_id, d1.id
                FROM deposit d1
                JOIN users u ON u.id = d1.user_id
                WHERE d1.status = ' . Deposit::$status['approved'] . '
                AND d1.id IN (
                    SELECT MIN(id)
                    FROM deposit
                    WHERE status = ' . Deposit::$status['approved'] . '
                    GROUP BY user_id
                )
                ' . ($startDate ? 'AND d1.approved_at >= "' . $startDate . '" ' : '') . '
                ' . ($endDate ? 'AND d1.approved_at <= "' . $endDate . '" ' : '') . '
                AND u.store_id IN (' . implode(',', $storeIds) . ')
            ) AS first_deposits'), function($join) {
                $join->on('first_deposits.user_id', '=', 'second_deposits.user_id');
            })
            ->whereIn('second_deposits.id', function ($query) {
                $query->select('d2.id')
                    ->from(DB::raw('(
                        SELECT user_id, id, amount, approved_at,
                               ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY approved_at) AS rn
                        FROM deposit
                        WHERE status = ' . Deposit::$status['approved'] . '
                    ) d2'))
                    ->where('d2.rn', 2);
            })
            ->where('second_deposits.status', Deposit::$status['approved'])
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('second_deposits.approved_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('second_deposits.approved_at', '<=', $endDate);
            })
            ->first();

        // Third-Time Deposits using the improved approach
        $totalTTD = Deposit::selectRaw('COUNT(DISTINCT third_deposits.user_id) AS count, SUM(third_deposits.amount) AS sum')
            ->from(DB::raw('deposit as third_deposits'))
            ->join(DB::raw('(
                SELECT d1.user_id, d1.id
                FROM deposit d1
                JOIN users u ON u.id = d1.user_id
                WHERE d1.status = ' . Deposit::$status['approved'] . '
                AND d1.id IN (
                    SELECT MIN(id)
                    FROM deposit
                    WHERE status = ' . Deposit::$status['approved'] . '
                    GROUP BY user_id
                )
                ' . ($startDate ? 'AND d1.approved_at >= "' . $startDate . '" ' : '') . '
                ' . ($endDate ? 'AND d1.approved_at <= "' . $endDate . '" ' : '') . '
                AND u.store_id IN (' . implode(',', $storeIds) . ')
            ) AS first_deposits'), function($join) {
                $join->on('first_deposits.user_id', '=', 'third_deposits.user_id');
            })
            ->join(DB::raw('(
                SELECT d2.user_id, d2.id
                FROM (
                    SELECT user_id, id, amount, approved_at,
                           ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY approved_at) AS rn
                    FROM deposit
                    WHERE status = ' . Deposit::$status['approved'] . '
                ) d2
                WHERE d2.rn = 2
            ) AS second_deposits'), function($join) {
                $join->on('second_deposits.user_id', '=', 'third_deposits.user_id');
            })
            ->whereIn('third_deposits.id', function ($query) {
                $query->select('d3.id')
                    ->from(DB::raw('(
                        SELECT user_id, id, amount, approved_at,
                               ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY approved_at) AS rn
                        FROM deposit
                        WHERE status = ' . Deposit::$status['approved'] . '
                    ) d3'))
                    ->where('d3.rn', 3);
            })
            ->where('third_deposits.status', Deposit::$status['approved'])
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('third_deposits.approved_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('third_deposits.approved_at', '<=', $endDate);
            })
            ->first();

        // Combined query for all transfer-related data
        $startDateTime = $startDate ? $startDate . ' 00:00:00' : null;
        $endDateTime = $endDate ? $endDate . ' 23:59:59' : null;

        $transferStats = DB::select("
            WITH
            wallet_transfers AS (
                SELECT
                    COUNT(CASE WHEN et.type = 1 THEN 1 END) as transfer_in_count,
                    COALESCE(SUM(CASE WHEN et.type = 1 THEN et.amount ELSE 0 END), 0) as transfer_in_sum,
                    COUNT(CASE WHEN et.type = 2 THEN 1 END) as transfer_out_count,
                    COALESCE(SUM(CASE WHEN et.type = 2 THEN et.amount ELSE 0 END), 0) as transfer_out_sum,
                    COALESCE(SUM(CASE WHEN et.type = 1 THEN et.amount ELSE -et.amount END), 0) as total_transfer_sum
                FROM ex_transfer et
                JOIN users u ON et.user_id = u.id
                WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                AND et.card_id IS NULL
                AND et.receivable_amount > 0
                AND et.status = " . ExTransfer::$status['confirmed'] . "
                " . ($startDateTime ? "AND et.created_at >= '$startDateTime'" : "") . "
                " . ($endDateTime ? "AND et.created_at <= '$endDateTime'" : "") . "
            ),
            card_transfers AS (
                SELECT
                    COUNT(CASE WHEN e.type = 1 THEN 1 END) as w2c_count,
                    COALESCE(SUM(CASE WHEN e.type = 1 THEN e.amount ELSE 0 END), 0) as w2c_sum,
                    COUNT(CASE WHEN e.type = 2 THEN 1 END) as c2w_count,
                    COALESCE(SUM(CASE WHEN e.type = 2 THEN e.amount ELSE 0 END), 0) as c2w_sum
                FROM ex_transfer e
                JOIN users u ON e.user_id = u.id
                WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                AND e.card_id IS NOT NULL
                AND e.status = 1
                " . ($startDateTime ? "AND e.created_at >= '$startDateTime'" : "") . "
                " . ($endDateTime ? "AND e.created_at <= '$endDateTime'" : "") . "
            )
            SELECT
                COALESCE(wt.transfer_in_count, 0) as transfer_in_count,
                COALESCE(wt.transfer_in_sum, 0) as transfer_in_sum,
                COALESCE(wt.transfer_out_count, 0) as transfer_out_count,
                COALESCE(wt.transfer_out_sum, 0) as transfer_out_sum,
                COALESCE(wt.total_transfer_sum, 0) as total_transfer_sum,
                COALESCE(ct.w2c_count, 0) as w2c_count,
                COALESCE(ct.w2c_sum, 0) as w2c_sum,
                COALESCE(ct.c2w_count, 0) as c2w_count,
                COALESCE(ct.c2w_sum, 0) as c2w_sum
            FROM wallet_transfers wt
            CROSS JOIN card_transfers ct
        ")[0];

        // Get promotion and adjustment data
        $promotionAndAdjustmentStats = DB::select("
            SELECT
                (
                    SELECT COUNT(*)
                    FROM user_promotions up
                    JOIN users u ON up.user_id = u.id
                    WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                    AND u.user_type = " . User::$userType['user-account'] . "
                    AND u.activated = 1
                    AND up.status = " . UserPromotion::$status['completed'] . "
                    " . ($startDate ? "AND up.created_at >= '$startDate'" : "") . "
                    " . ($endDate ? "AND up.created_at <= '$endDate'" : "") . "
                ) as promotion_count,
                (
                    SELECT COUNT(*)
                    FROM user_promotions up
                    JOIN users u ON up.user_id = u.id
                    WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                    AND u.user_type = " . User::$userType['user-account'] . "
                    AND u.activated = 1
                    AND up.status = " . UserPromotion::$status['completed'] . "
                    AND up.bonus_amount > 0
                    " . ($startDate ? "AND up.created_at >= '$startDate'" : "") . "
                    " . ($endDate ? "AND up.created_at <= '$endDate'" : "") . "
                ) as promotion_in_count,
                (
                    SELECT COUNT(*)
                    FROM user_promotions up
                    JOIN users u ON up.user_id = u.id
                    WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                    AND u.user_type = " . User::$userType['user-account'] . "
                    AND u.activated = 1
                    AND up.status = " . UserPromotion::$status['completed'] . "
                    AND up.game_return_amount > 0
                    " . ($startDate ? "AND up.created_at >= '$startDate'" : "") . "
                    " . ($endDate ? "AND up.created_at <= '$endDate'" : "") . "
                ) as promotion_out_count,
                (
                    SELECT COALESCE(SUM(up.bonus_amount), 0)
                    FROM user_promotions up
                    JOIN users u ON up.user_id = u.id
                    WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                    AND u.user_type = " . User::$userType['user-account'] . "
                    AND u.activated = 1
                    AND up.status = " . UserPromotion::$status['completed'] . "
                    " . ($startDate ? "AND up.created_at >= '$startDate'" : "") . "
                    " . ($endDate ? "AND up.created_at <= '$endDate'" : "") . "
                ) as promotion_in_sum,
                (
                    SELECT COALESCE(SUM(up.game_return_amount), 0)
                    FROM user_promotions up
                    JOIN users u ON up.user_id = u.id
                    WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                    AND u.user_type = " . User::$userType['user-account'] . "
                    AND u.activated = 1
                    AND up.status = " . UserPromotion::$status['completed'] . "
                    " . ($startDate ? "AND up.created_at >= '$startDate'" : "") . "
                    " . ($endDate ? "AND up.created_at <= '$endDate'" : "") . "
                ) as promotion_out_sum,
                (
                    SELECT COALESCE(SUM(up.burn_amount), 0)
                    FROM user_promotions up
                    JOIN users u ON up.user_id = u.id
                    WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                    AND u.user_type = " . User::$userType['user-account'] . "
                    AND u.activated = 1
                    AND up.status = " . UserPromotion::$status['completed'] . "
                    " . ($startDate ? "AND up.created_at >= '$startDate'" : "") . "
                    " . ($endDate ? "AND up.created_at <= '$endDate'" : "") . "
                ) as promotion_burnt_sum,
                (
                    SELECT COUNT(*)
                    FROM credit_transaction ct
                    JOIN users u ON ct.user_id = u.id
                    WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                    AND u.user_type = " . User::$userType['user-account'] . "
                    AND u.activated = 1
                    AND ct.subject_type = 1
                    " . ($startDate ? "AND ct.created_at >= '$startDate'" : "") . "
                    " . ($endDate ? "AND ct.created_at <= '$endDate'" : "") . "
                ) as adjustment_in_count,
                (
                    SELECT COALESCE(SUM(ct.amount), 0)
                    FROM credit_transaction ct
                    JOIN users u ON ct.user_id = u.id
                    WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                    AND u.user_type = " . User::$userType['user-account'] . "
                    AND u.activated = 1
                    AND ct.subject_type = 1
                    " . ($startDate ? "AND ct.created_at >= '$startDate'" : "") . "
                    " . ($endDate ? "AND ct.created_at <= '$endDate'" : "") . "
                ) as adjustment_in_sum,
                (
                    SELECT COUNT(*)
                    FROM credit_transaction ct
                    JOIN users u ON ct.user_id = u.id
                    WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                    AND u.user_type = " . User::$userType['user-account'] . "
                    AND u.activated = 1
                    AND ct.subject_type = 2
                    " . ($startDate ? "AND ct.created_at >= '$startDate'" : "") . "
                    " . ($endDate ? "AND ct.created_at <= '$endDate'" : "") . "
                ) as adjustment_out_count,
                (
                    SELECT COALESCE(SUM(ct.amount), 0)
                    FROM credit_transaction ct
                    JOIN users u ON ct.user_id = u.id
                    WHERE u.store_id IN (" . implode(',', $storeIds) . ")
                    AND u.user_type = " . User::$userType['user-account'] . "
                    AND u.activated = 1
                    AND ct.subject_type = 2
                    " . ($startDate ? "AND ct.created_at >= '$startDate'" : "") . "
                    " . ($endDate ? "AND ct.created_at <= '$endDate'" : "") . "
                ) as adjustment_out_sum
        ")[0];

        // Get p2p transfer data
        $p2pStats = DB::select("
            SELECT
                COUNT(DISTINCT t.to_id) as p2p_count,
                COALESCE(SUM(t.amount), 0) as p2p_sum
            FROM transfer t
            JOIN users u ON t.to_id = u.id
            WHERE t.from_id IN (1002696, 1002990, 1002702, 1002737, 1002692, 1002696, 1002790, 1002841, 1002990, 1003450)
            AND u.store_id IN (" . implode(',', $storeIds) . ")
            " . ($startDate ? "AND t.created_at >= '$startDate'" : "") . "
            " . ($endDate ? "AND t.created_at <= '$endDate'" : "") . "
        ")[0];

        // Calculate derived values
        $totalNettDeposits = $totalDeposits->sum - $withdrawalStats->withdrawal_sum;

        // Calculate rates - updated logic for TTD rate
        $totalSTDRate = ($totalFTD->count > 0) ? ($totalSTD->count / $totalFTD->count) * 100 : 0;
        $totalTTDRate = ($totalSTD->count > 0) ? ($totalTTD->count / $totalSTD->count) * 100 : 0;

        // Retention rate - using original logic
        $retentionRate = $totalActiveUsersLastMonth > 0 ?
            (($totalActiveUsers - $totalFTD->count) / $totalActiveUsersLastMonth) * 100 : 0;

        // Total adjustment nett
        $totalAdjustmentNett = $promotionAndAdjustmentStats->adjustment_in_sum - $promotionAndAdjustmentStats->adjustment_out_sum;

        // Advance credit
        $advanceCreditStats = DB::select("
            SELECT
                COUNT(*) as advance_credit_count,
                COALESCE(SUM(amount), 0) as advance_credit_sum
            FROM credit_transaction
            JOIN users ON users.id = credit_transaction.user_id
            WHERE users.store_id IN (" . implode(',', $storeIds) . ")
            AND subject_type = 105
            " . ($startDate ? "AND credit_transaction.created_at >= '$startDate'" : "") . "
            " . ($endDate ? "AND credit_transaction.created_at <= '$endDate'" : "") . "
        ")[0];

        abort(200, json_encode([
            'data' => [
                'totalRegistrations' => $totalRegistrations ?? null,
                'totalActiveUsers' => $totalActiveUsers ?? null,
                'totalDeposits' => [
                    'count' => $totalDeposits->count ?? 0,
                    'sum' => $totalDeposits->sum,
                ],
                'totalWithdrawals' => [
                    'count' => $withdrawalStats->withdrawal_count,
                    'sum' => $withdrawalStats->withdrawal_sum,
                ],
                'totalNettDeposits' => $totalNettDeposits,
                'totalFTD' => [
                    'count' => $totalFTD->count ?? 0,
                    'sum' => $totalFTD->sum,
                ],
                'totalTurnover' => [
                    'count' => $turnoverStats->turnover_count,
                    'sum' => $turnoverStats->turnover_sum
                ],
                'transferDetails' => [
                    'transfer_in' => [
                        'count' => $transferStats->transfer_in_count,
                        'sum' => $transferStats->transfer_in_sum,
                    ],
                    'transfer_out' => [
                        'count' => $transferStats->transfer_out_count,
                        'sum' => $transferStats->transfer_out_sum,
                    ]
                ],
                'totalTransfer' => [
                    'sum' => $transferStats->total_transfer_sum
                ],
                'retentionRate' => $retentionRate,
                'totalSTD' => [
                    'count' => $totalSTD->count ?? 0,
                    'sum' => $totalSTD->sum,
                ],
                'totalSTDRate' => $totalSTDRate,
                'totalTTD' => [
                    'count' => $totalTTD->count ?? 0,
                    'sum' => $totalTTD->sum,
                ],
                'totalTTDRate' => $totalTTDRate,
                'totalPromotion' => [
                    'count' => $promotionAndAdjustmentStats->promotion_count,
                    'promotion_in_count' => $promotionAndAdjustmentStats->promotion_in_count,
                    'promotion_out_count' => $promotionAndAdjustmentStats->promotion_out_count,
                    'promotion_in' => $promotionAndAdjustmentStats->promotion_in_sum,
                    'promotion_out' => $promotionAndAdjustmentStats->promotion_out_sum,
                    'promotion_burnt' => $promotionAndAdjustmentStats->promotion_burnt_sum,
                ],
                'totalAdjustment' => [
                    'adjustment_in' => [
                        'count' => $promotionAndAdjustmentStats->adjustment_in_count,
                        'sum' => $promotionAndAdjustmentStats->adjustment_in_sum
                    ],
                    'adjustment_out' => [
                        'count' => $promotionAndAdjustmentStats->adjustment_out_count,
                        'sum' => $promotionAndAdjustmentStats->adjustment_out_sum,
                    ],
                    'adjustment_nett' => $totalAdjustmentNett
                ],
                'walletToCard' => [
                    'count' => $transferStats->w2c_count,
                    'sum' => $transferStats->w2c_sum,
                ],
                'cardToWallet' => [
                    'count' => $transferStats->c2w_count,
                    'sum' => $transferStats->c2w_sum,
                ],
                'p2pTransferIn' => [
                    'count' => $p2pStats->p2p_count,
                    'sum' => $p2pStats->p2p_sum,
                ],
                'totalAdvanceCredit' => [
                    'count' => $advanceCreditStats->advance_credit_count,
                    'sum' => $advanceCreditStats->advance_credit_sum,
                ],
            ],
        ]));
    }

    public function newMembers(Request $request)
    {
        $storeIds = $request->store_ids;
        $startDate = $request->start_date;
        $endDate = $request->end_date;

        // Get new members (total registrations) with only id, name, and phone_no
        $newMembers = User::select(['id', 'name', 'phone_no'])
            ->whereIn('store_id', $storeIds)
            ->user()
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->get();

        abort(200, json_encode([
            'data' => $newMembers
        ]));
    }

    public function deposit(Request $request)
    {
        $storeIds = $request->store_ids;
        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $currentPage = $request->current_page ?? 1;
        $perPage = $request->per_page;
        $currentUrl = $request->current_url;

        $searchQuery = '&search=' . $request->search . '&startDate=' . $startDate . '&endDate=' . $endDate;

        $data = [];
        $stores = Store::whereIn('store_id', $storeIds)
            ->active()
            ->get();

        foreach ($stores as $store) {
            $totalRecharge = UserCardLog::whereHas('user', function ($query) use ($store) {
                $query->where('store_id', $store->id)
                    ->user()
                    ->active();
            })
                ->select(DB::raw('
                    COUNT(*) as count,
                    COALESCE(SUM(operation_qty), 0) AS sum
                '))
                ->deposit()
                ->when($startDate, function ($query) use ($startDate) {
                    return $query->whereDate('transaction_at', '>=', $startDate);
                })
                ->when($endDate, function ($query) use ($endDate) {
                    return $query->whereDate('transaction_at', '<=', $endDate);
                })
                ->first();

            $totalOnlineDeposit = Deposit::whereHas('user', function ($query) use ($store) {
                $query->where('store_id', $store->id)
                    ->user()
                    ->active();
            })
                ->select(DB::raw('
                    COUNT(*) as count,
                    COALESCE(SUM(amount), 0) AS sum
                '))
                ->approved()
                ->when($startDate, function ($query) use ($startDate) {
                    return $query->whereDate('approved_at', '>=', $startDate);
                })
                ->when($endDate, function ($query) use ($endDate) {
                    return $query->whereDate('approved_at', '<=', $endDate);
                })
                ->first();

            $totalW2C = ExTransfer::whereHas('user', function ($query) use ($store) {
                $query->where('store_id', $store->id)
                    ->user()
                    ->active();
            })
                ->select(DB::raw('
                    COUNT(*) as count,
                    COALESCE(SUM(amount), 0) AS sum
                '))
                ->in()
                ->confirmed()
                ->whereNotNull('card_id')
                ->whereNull('product_id')
                ->when($startDate, function ($query) use ($startDate) {
                    return $query->whereDate('created_at', '>=', $startDate);
                })
                ->when($endDate, function ($query) use ($endDate) {
                    return $query->whereDate('created_at', '<=', $endDate);
                })
                ->first();

            $data[$store->store_id] = [
                'store' => $store->name,
                'total_recharge' => [
                    'count' => $totalRecharge->count,
                    'sum' => number_format($totalRecharge->sum, 2, '.', ','),
                ],
                'total_online_deposit' => [
                    'count' => $totalOnlineDeposit->count,
                    'sum' => number_format($totalOnlineDeposit->sum, 2, '.', ','),
                ],
                'total_deposit' => [
                    'count' => $totalRecharge->count + $totalOnlineDeposit->count,
                    'sum' => number_format($totalRecharge->sum + $totalOnlineDeposit->sum, 2, '.', ',')
                ],
                'total_wallet_2_card' => [
                    'count' => $totalW2C->count,
                    'sum' => number_format($totalW2C->sum, 2, '.', ','),
                ]
            ];
        }

        $items = array_slice($data, ($currentPage - 1) * $perPage, $perPage);
        $paginator = new LengthAwarePaginator($items, count($data), $perPage, $currentPage);
        $links = [];

        if ($paginator->lastPage() != 1) {
            $links[] =  [
                'url' => $currentUrl . '?perPage=' . $perPage . '&page=' . $paginator->lastPage() . $searchQuery,
                'label' => $paginator->lastPage(),
                'active' => false
            ];
        }

        abort(200, json_encode([
            'data' => [
                'items' => $paginator->items(),
                'total' => $paginator->total(),
                'page' => [
                    'links' => $links,
                    'first_page_url' => $currentUrl . '?perPage=' . $perPage . '&page=1' . $searchQuery,
                    'last_page_url' => $currentUrl . '?perPage=' . $perPage . '&page=' . $paginator->lastPage() . $searchQuery,
                    'prev_page_url' => $paginator->previousPageUrl(),
                    'next_page_url' => $paginator->nextPageUrl(),
                    'total' => $paginator->total(),
                    'per_page' => $perPage,
                    'current_page' => $currentPage,
                ]
            ],
        ]));
    }

    public function withdrawal(Request $request)
    {
        $storeIds = $request->store_ids;
        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $currentPage = $request->current_page ?? 1;
        $perPage = $request->per_page;
        $currentUrl = $request->current_url;

        $searchQuery = '&search=' . $request->search . '&startDate=' . $startDate . '&endDate=' . $endDate;

        $stores = Store::whereIn('store_id', $storeIds)
            ->active()
            ->get();

        foreach ($stores as $store) {
            $totalElimination = UserCardLog::whereHas('user', function ($query) use ($store) {
                $query->where('store_id', $store->id)
                    ->user()
                    ->active();
            })
                ->select(DB::raw('
                        COUNT(*) as count,
                        COALESCE(SUM(operation_qty), 0) AS sum
                    '))
                ->withdrawal()
                ->when($startDate, function ($query) use ($startDate) {
                    return $query->where('transaction_at', '>=', $startDate);
                })
                ->when($endDate, function ($query) use ($endDate) {
                    return $query->where('transaction_at', '<=', $endDate);
                })
                ->first();

            $totalOnlineWithdrawal = Withdrawal::whereHas('user', function ($query) use ($store) {
                $query->where('store_id', $store->id)
                    ->user()
                    ->active();
            })
                ->select(DB::raw('
                        COUNT(*) as count,
                        COALESCE(SUM(amount), 0) AS sum
                    '))
                ->approved()
                ->when($startDate, function ($query) use ($startDate) {
                    return $query->where('updated_at', '>=', $startDate);
                })
                ->when($endDate, function ($query) use ($endDate) {
                    return $query->where('updated_at', '<=', $endDate);
                })
                ->first();

            $totalC2W = ExTransfer::whereHas('user', function ($query) use ($store) {
                $query->where('store_id', $store->id)
                    ->user()
                    ->active();
            })
                ->select(DB::raw('
                        COUNT(*) as count,
                        COALESCE(SUM(amount), 0) AS sum
                    '))
                ->out()
                ->confirmed()
                ->whereNotNull('card_id')
                ->whereNull('product_id')
                ->when($startDate, function ($query) use ($startDate) {
                    return $query->where('created_at', '>=', $startDate);
                })
                ->when($endDate, function ($query) use ($endDate) {
                    return $query->where('created_at', '<=', $endDate);
                })
                ->first();

            $data[$store->store_id] = [
                'store' => $store->name,
                'total_elimination' => [
                    'count' => $totalElimination->count,
                    'sum' => number_format($totalElimination->sum, 2, '.', ','),
                ],
                'total_online_withdrawal' => [
                    'count' => $totalOnlineWithdrawal->count,
                    'sum' => number_format($totalOnlineWithdrawal->sum, 2, '.', ','),
                ],
                'total_withdrawal' => [
                    'count' => $totalElimination->count + $totalOnlineWithdrawal->count,
                    'sum' => number_format($totalElimination->sum + $totalOnlineWithdrawal->sum, 2, '.', ',')
                ],
                'total_card_2_wallet' => [
                    'count' => $totalC2W->count,
                    'sum' => number_format($totalC2W->sum, 2, '.', ','),
                ]
            ];
        }

        $items = array_slice($data, ($currentPage - 1) * $perPage, $perPage);
        $paginator = new LengthAwarePaginator($items, count($data), $perPage, $currentPage);
        $links = [];

        if ($paginator->lastPage() != 1) {
            $links[] =  [
                'url' => url()->current() . '?perPage=' . $perPage . '&page=' . $paginator->lastPage() . $searchQuery,
                'label' => $paginator->lastPage(),
                'active' => false
            ];
        }

        abort(200, json_encode([
            'data' => [
                'items' => $paginator->items(),
                'total' => $paginator->total(),
                'page' => [
                    'links' => $links,
                    'first_page_url' => $currentUrl . '?perPage=' . $perPage . '&page=1' . $searchQuery,
                    'last_page_url' => $currentUrl . '?perPage=' . $perPage . '&page=' . $paginator->lastPage() . $searchQuery,
                    'prev_page_url' => $paginator->previousPageUrl(),
                    'next_page_url' => $paginator->nextPageUrl(),
                    'total' => $paginator->total(),
                    'per_page' => $perPage,
                    'current_page' => $currentPage,
                ]
            ],
        ]));
    }

    public function bulkStatistics(Request $request)
    {
        $storeIds = $request->store_ids;
        $startDate = $request->start_date;
        $endDate = $request->end_date;

        $lastMonthStartDate = Carbon::parse($request->start_date)
            ->subMonth()
            ->startOfMonth();

        $lastMonthEndDate = Carbon::parse($request->start_date)
            ->subMonth()
            ->endOfMonth();

        // Get all stores in one query
        $stores = Store::whereIn('store_id', $storeIds)
            ->active()
            ->get();

        // Keep original logic for registrations and active users
        $totalRegistrationsByStore = [];
        $activeUsersByStore = [];
        $activeUsersLastMonthByStore = [];

        foreach ($storeIds as $storeId) {
            // Total Registrations - exactly as original
            $totalRegistrations = User::where('store_id', $storeId)
                ->user()
                ->when($startDate, function ($query) use ($startDate) {
                    return $query->where('created_at', '>=', $startDate);
                })
                ->when($endDate, function ($query) use ($endDate) {
                    return $query->where('created_at', '<=', $endDate);
                })
                ->count();

            // Total Active Users - exactly as original
            $totalActiveUsers = User::whereHas('deposits', function (Builder $query) use ($startDate, $endDate) {
                $query->when($startDate, function ($query) use ($startDate) {
                    return $query->where('approved_at', '>=', $startDate);
                })
                    ->when($endDate, function ($query) use ($endDate) {
                        return $query->where('approved_at', '<=', $endDate);
                    });
            })
                ->where('store_id', $storeId)
                ->user()
                ->count();

            // Total Active Users Last Month - for retention calculation
            $activeUsersLastMonth = User::whereHas('deposits', function (Builder $query) use ($lastMonthStartDate, $lastMonthEndDate) {
                $query->whereBetween('approved_at', [$lastMonthStartDate, $lastMonthEndDate]);
            })
                ->where('store_id', $storeId)
                ->user()
                ->active()
                ->count();

            $totalRegistrationsByStore[$storeId] = $totalRegistrations;
            $activeUsersByStore[$storeId] = $totalActiveUsers;
            $activeUsersLastMonthByStore[$storeId] = $activeUsersLastMonth;
        }

        // Optimize deposits query - fixed FTD calculation to consider date range and sum only first deposits
        $depositStats = Deposit::selectRaw('
                users.store_id,
                COUNT(*) as deposit_count,
                SUM(deposit.amount) as deposit_sum,
                COUNT(DISTINCT CASE
                    WHEN deposit.id IN (
                        SELECT MIN(id)
                        FROM deposit
                        WHERE status = ' . Deposit::$status['approved'] . '
                        GROUP BY user_id
                    )
                    AND deposit.approved_at >= "' . ($startDate ?: '1970-01-01') . '"
                    AND deposit.approved_at <= "' . ($endDate ?: '2099-12-31') . '"
                    THEN deposit.user_id
                    END) as ftd_count,
                SUM(CASE
                    WHEN deposit.id IN (
                        SELECT MIN(id)
                        FROM deposit
                        WHERE status = ' . Deposit::$status['approved'] . '
                        GROUP BY user_id
                    )
                    AND deposit.approved_at >= "' . ($startDate ?: '1970-01-01') . '"
                    AND deposit.approved_at <= "' . ($endDate ?: '2099-12-31') . '"
                    THEN deposit.amount
                    ELSE 0
                    END) as ftd_sum
            ')
            ->join('users', 'users.id', '=', 'deposit.user_id')
            ->whereIn('users.store_id', $storeIds)
            ->approved()
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('deposit.approved_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('deposit.approved_at', '<=', $endDate);
            })
            ->groupBy('users.store_id')
            ->get()
            ->keyBy('store_id');

        // Optimize withdrawals query
        $withdrawalStats = Withdrawal::selectRaw('
                users.store_id,
                COUNT(*) as withdrawal_count,
                SUM(withdrawal.amount) as withdrawal_sum
            ')
            ->join('users', 'users.id', '=', 'withdrawal.user_id')
            ->whereIn('users.store_id', $storeIds)
            ->approved()
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('withdrawal.updated_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('withdrawal.updated_at', '<=', $endDate);
            })
            ->groupBy('users.store_id')
            ->get()
            ->keyBy('store_id');

        // Optimize transfer statistics
        $transferStats = ExTransfer::selectRaw('
                users.store_id,
                COUNT(CASE WHEN type = 1 THEN 1 END) as transfer_in_count,
                SUM(CASE WHEN type = 1 THEN amount ELSE 0 END) as transfer_in_sum,
                COUNT(CASE WHEN type = 2 THEN 1 END) as transfer_out_count,
                SUM(CASE WHEN type = 2 THEN amount ELSE 0 END) as transfer_out_sum,
                SUM(CASE WHEN type = 1 THEN amount ELSE -amount END) as total_transfer
            ')
            ->join('users', 'users.id', '=', 'ex_transfer.user_id')
            ->whereIn('users.store_id', $storeIds)
            ->whereNull('card_id')
            ->where('receivable_amount', '>', 0)
            ->confirmed()
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('ex_transfer.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('ex_transfer.created_at', '<=', $endDate);
            })
            ->groupBy('users.store_id')
            ->get()
            ->keyBy('store_id');

        // Optimize card transfer statistics
        $cardTransferStats = ExTransfer::selectRaw('
                users.store_id,
                COUNT(CASE WHEN type = 1 THEN 1 END) as w2c_count,
                SUM(CASE WHEN type = 1 THEN amount ELSE 0 END) as w2c_sum,
                COUNT(CASE WHEN type = 2 THEN 1 END) as c2w_count,
                SUM(CASE WHEN type = 2 THEN amount ELSE 0 END) as c2w_sum
            ')
            ->join('users', 'users.id', '=', 'ex_transfer.user_id')
            ->whereIn('users.store_id', $storeIds)
            ->whereNotNull('card_id')
            ->where('status', 1)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('ex_transfer.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('ex_transfer.created_at', '<=', $endDate);
            })
            ->groupBy('users.store_id')
            ->get()
            ->keyBy('store_id');

        // Optimize P2P transfer statistics
        $p2pStats = Transfer::selectRaw('
                uto.store_id,
                COUNT(DISTINCT transfer.to_id) as p2p_count,
                SUM(transfer.amount) as p2p_sum
            ')
            ->join('users as uto', 'uto.id', '=', 'transfer.to_id')
            ->whereIn('transfer.from_id', [1002696, 1002990, 1002702, 1002737, 1002692, 1002790, 1002841, 1003450])
            ->whereIn('uto.store_id', $storeIds)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('transfer.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('transfer.created_at', '<=', $endDate);
            })
            ->groupBy('uto.store_id')
            ->get()
            ->keyBy('store_id');

        // Optimize adjustment statistics
        $adjustmentStats = CreditTransaction::selectRaw('
                users.store_id,
                COUNT(CASE WHEN subject_type = 1 THEN 1 END) as adjustment_in_count,
                SUM(CASE WHEN subject_type = 1 THEN amount ELSE 0 END) as adjustment_in_sum,
                COUNT(CASE WHEN subject_type = 2 THEN 1 END) as adjustment_out_count,
                SUM(CASE WHEN subject_type = 2 THEN amount ELSE 0 END) as adjustment_out_sum
            ')
            ->join('users', 'users.id', '=', 'credit_transaction.user_id')
            ->whereIn('users.store_id', $storeIds)
            ->where('users.user_type', User::$userType['user-account'])
            ->where('users.activated', true)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('credit_transaction.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('credit_transaction.created_at', '<=', $endDate);
            })
            ->groupBy('users.store_id')
            ->get()
            ->keyBy('store_id');

        // Optimize second-time deposits query - improved to match the SQL example
        $stdStats = DB::table('deposit as second_deposits')
            ->selectRaw('
                u.store_id,
                COUNT(DISTINCT second_deposits.user_id) as std_count,
                SUM(second_deposits.amount) as std_sum
            ')
            ->join('users as u', 'u.id', '=', 'second_deposits.user_id')
            ->join(DB::raw('(
                SELECT d1.user_id, d1.id
                FROM deposit d1
                JOIN users u ON u.id = d1.user_id
                WHERE d1.status = ' . Deposit::$status['approved'] . '
                AND d1.id IN (
                    SELECT MIN(id)
                    FROM deposit
                    WHERE status = ' . Deposit::$status['approved'] . '
                    GROUP BY user_id
                )
                ' . ($startDate ? 'AND d1.approved_at >= "' . $startDate . '" ' : '') . '
                ' . ($endDate ? 'AND d1.approved_at <= "' . $endDate . '" ' : '') . '
                AND u.store_id IN (' . implode(',', $storeIds) . ')
            ) AS first_deposits'), function($join) {
                $join->on('first_deposits.user_id', '=', 'second_deposits.user_id');
            })
            ->whereIn('second_deposits.id', function ($query) {
                $query->select('d2.id')
                    ->from(DB::raw('(
                        SELECT user_id, id, amount, approved_at,
                               ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY approved_at) AS rn
                        FROM deposit
                        WHERE status = ' . Deposit::$status['approved'] . '
                    ) d2'))
                    ->where('d2.rn', 2);
            })
            ->where('second_deposits.status', Deposit::$status['approved'])
            ->whereIn('u.store_id', $storeIds)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('second_deposits.approved_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('second_deposits.approved_at', '<=', $endDate);
            })
            ->groupBy('u.store_id')
            ->get()
            ->keyBy('store_id');

        // Optimize third-time deposits query - improved to match the SQL example
        $ttdStats = DB::table('deposit as third_deposits')
            ->selectRaw('
                u.store_id,
                COUNT(DISTINCT third_deposits.user_id) as ttd_count,
                SUM(third_deposits.amount) as ttd_sum
            ')
            ->join('users as u', 'u.id', '=', 'third_deposits.user_id')
            ->join(DB::raw('(
                SELECT d1.user_id, d1.id
                FROM deposit d1
                JOIN users u ON u.id = d1.user_id
                WHERE d1.status = ' . Deposit::$status['approved'] . '
                AND d1.id IN (
                    SELECT MIN(id)
                    FROM deposit
                    WHERE status = ' . Deposit::$status['approved'] . '
                    GROUP BY user_id
                )
                ' . ($startDate ? 'AND d1.approved_at >= "' . $startDate . '" ' : '') . '
                ' . ($endDate ? 'AND d1.approved_at <= "' . $endDate . '" ' : '') . '
                AND u.store_id IN (' . implode(',', $storeIds) . ')
            ) AS first_deposits'), function($join) {
                $join->on('first_deposits.user_id', '=', 'third_deposits.user_id');
            })
            ->join(DB::raw('(
                SELECT d2.user_id, d2.id
                FROM (
                    SELECT user_id, id, amount, approved_at,
                           ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY approved_at) AS rn
                    FROM deposit
                    WHERE status = ' . Deposit::$status['approved'] . '
                ) d2
                WHERE d2.rn = 2
            ) AS second_deposits'), function($join) {
                $join->on('second_deposits.user_id', '=', 'third_deposits.user_id');
            })
            ->whereIn('third_deposits.id', function ($query) {
                $query->select('d3.id')
                    ->from(DB::raw('(
                        SELECT user_id, id, amount, approved_at,
                               ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY approved_at) AS rn
                        FROM deposit
                        WHERE status = ' . Deposit::$status['approved'] . '
                    ) d3'))
                    ->where('d3.rn', 3);
            })
            ->where('third_deposits.status', Deposit::$status['approved'])
            ->whereIn('u.store_id', $storeIds)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('third_deposits.approved_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('third_deposits.approved_at', '<=', $endDate);
            })
            ->groupBy('u.store_id')
            ->get()
            ->keyBy('store_id');

        // Optimize promotion statistics
        $promotionStats = UserPromotion::selectRaw('
                users.store_id,
                COUNT(*) as promotion_count,
                SUM(bonus_amount) as promotion_in,
                SUM(game_return_amount) as promotion_out,
                SUM(burn_amount) as promotion_burnt
            ')
            ->join('users', 'users.id', '=', 'user_promotions.user_id')
            ->whereIn('users.store_id', $storeIds)
            ->completed()
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('user_promotions.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('user_promotions.created_at', '<=', $endDate);
            })
            ->groupBy('users.store_id')
            ->get()
            ->keyBy('store_id');

        // Optimize advance credit statistics
        $advanceCreditStats = DB::table('credit_transaction')
            ->selectRaw('
                users.store_id,
                COUNT(*) as advance_credit_count,
                SUM(credit_transaction.amount) as advance_credit_sum
            ')
            ->join('users', 'users.id', '=', 'credit_transaction.user_id')
            ->whereIn('users.store_id', $storeIds)
            ->where('subject_type', 105) // advanced-credit
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('credit_transaction.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('credit_transaction.created_at', '<=', $endDate);
            })
            ->groupBy('users.store_id')
            ->get()
            ->keyBy('store_id');

        // After the depositStats query, add this new query for P2P FTD - fixed to consider date range and sum only first transfers
        $p2pFtdStats = Transfer::selectRaw('
        uto.store_id,
        COUNT(DISTINCT CASE
            WHEN transfer.id IN (
                SELECT MIN(id)
                FROM transfer
                WHERE from_id IN (1002696, 1002990, 1002702, 1002737, 1002692, 1002790, 1002841, 1003450)
                GROUP BY to_id
            )
            AND transfer.created_at >= "' . ($startDate ?: '1970-01-01') . '"
            AND transfer.created_at <= "' . ($endDate ?: '2099-12-31') . '"
            THEN transfer.to_id
            END) as p2p_ftd_count,
        SUM(CASE
            WHEN transfer.id IN (
                SELECT MIN(id)
                FROM transfer
                WHERE from_id IN (1002696, 1002990, 1002702, 1002737, 1002692, 1002790, 1002841, 1003450)
                GROUP BY to_id
            )
            AND transfer.created_at >= "' . ($startDate ?: '1970-01-01') . '"
            AND transfer.created_at <= "' . ($endDate ?: '2099-12-31') . '"
            THEN transfer.amount
            ELSE 0
            END) as p2p_ftd_sum
    ')
            ->join('users as uto', 'uto.id', '=', 'transfer.to_id')
            ->whereIn('transfer.from_id', [1002696, 1002990, 1002702, 1002737, 1002692, 1002790, 1002841, 1003450])
            ->whereIn('uto.store_id', $storeIds)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('transfer.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('transfer.created_at', '<=', $endDate);
            })
            ->groupBy('uto.store_id')
            ->get()
            ->keyBy('store_id');


        $result = [];
        foreach ($stores as $store) {
            $storeId = $store->store_id;

            // Calculate retention rate
            $totalActiveUsers = $activeUsersByStore[$storeId] ?? 0;
            $ftdCount = $depositStats[$storeId]->ftd_count ?? 0;
            $totalActiveUsersLastMonth = $activeUsersLastMonthByStore[$storeId] ?? 0;
            $retentionRate = $totalActiveUsersLastMonth > 0 ? ($totalActiveUsers - $ftdCount) / $totalActiveUsersLastMonth * 100 : 0;

            // Calculate STD and TTD rates - updated TTD rate calculation
            $stdRate = $ftdCount > 0 ? ($stdStats[$storeId]->std_count ?? 0) / $ftdCount * 100 : 0;
            $stdCount = $stdStats[$storeId]->std_count ?? 0;
            $ttdRate = $stdCount > 0 ? ($ttdStats[$storeId]->ttd_count ?? 0) / $stdCount * 100 : 0;

            $result[] = [
                'storeId' => $storeId,
                'merchantName' => $store->name,
                'totalRegistrations' => $totalRegistrationsByStore[$storeId] ?? 0,
                'totalActiveUsers' => $activeUsersByStore[$storeId] ?? 0,
                'totalActiveUsersLastMonth' => $activeUsersLastMonthByStore[$storeId] ?? 0,
                'retentionRate' => $retentionRate,
                'totalDepositsCount' => $depositStats[$storeId]->deposit_count ?? 0,
                'totalDeposits' => $depositStats[$storeId]->deposit_sum ?? 0,
                'totalWithdrawalsCount' => $withdrawalStats[$storeId]->withdrawal_count ?? 0,
                'totalWithdrawals' => $withdrawalStats[$storeId]->withdrawal_sum ?? 0,
                'totalNettDeposits' => ($depositStats[$storeId]->deposit_sum ?? 0) - ($withdrawalStats[$storeId]->withdrawal_sum ?? 0),
                'totalFTDCount' => ($depositStats[$storeId]->ftd_count ?? 0) + ($p2pFtdStats[$storeId]->p2p_ftd_count ?? 0),
                'totalFTD' => ($depositStats[$storeId]->ftd_sum ?? 0) + ($p2pFtdStats[$storeId]->p2p_ftd_sum ?? 0),
                'totalSTDCount' => $stdStats[$storeId]->std_count ?? 0,
                'totalSTD' => $stdStats[$storeId]->std_sum ?? 0,
                'totalSTDRate' => $stdRate,
                'totalTTDCount' => $ttdStats[$storeId]->ttd_count ?? 0,
                'totalTTD' => $ttdStats[$storeId]->ttd_sum ?? 0,
                'totalTTDRate' => $ttdRate,
                'transferInCount' => $transferStats[$storeId]->transfer_in_count ?? 0,
                'transferInSum' => $transferStats[$storeId]->transfer_in_sum ?? 0,
                'transferOutCount' => $transferStats[$storeId]->transfer_out_count ?? 0,
                'transferOutSum' => $transferStats[$storeId]->transfer_out_sum ?? 0,
                'totalTransfer' => $transferStats[$storeId]->total_transfer ?? 0,
                'totalWin' => 0,
                'totalLoss' => 0,
                'walletToCardCount' => $cardTransferStats[$storeId]->w2c_count ?? 0,
                'walletToCardSum' => $cardTransferStats[$storeId]->w2c_sum ?? 0,
                'cardToWalletCount' => $cardTransferStats[$storeId]->c2w_count ?? 0,
                'cardToWalletSum' => $cardTransferStats[$storeId]->c2w_sum ?? 0,
                'p2pTransferInCount' => $p2pStats[$storeId]->p2p_count ?? 0,
                'p2pTransferInSum' => $p2pStats[$storeId]->p2p_sum ?? 0,
                'adjustmentInCount' => $adjustmentStats[$storeId]->adjustment_in_count ?? 0,
                'adjustmentInSum' => $adjustmentStats[$storeId]->adjustment_in_sum ?? 0,
                'adjustmentOutCount' => $adjustmentStats[$storeId]->adjustment_out_count ?? 0,
                'adjustmentOutSum' => $adjustmentStats[$storeId]->adjustment_out_sum ?? 0,
                'adjustmentNett' => ($adjustmentStats[$storeId]->adjustment_in_sum ?? 0) - ($adjustmentStats[$storeId]->adjustment_out_sum ?? 0),
                'promotionCount' => $promotionStats[$storeId]->promotion_count ?? 0,
                'promotionIn' => $promotionStats[$storeId]->promotion_in ?? 0,
                'promotionOut' => $promotionStats[$storeId]->promotion_out ?? 0,
                'promotionBurnt' => $promotionStats[$storeId]->promotion_burnt ?? 0,
                'advanceCreditCount' => $advanceCreditStats[$storeId]->advance_credit_count ?? 0,
                'advanceCreditSum' => $advanceCreditStats[$storeId]->advance_credit_sum ?? 0
            ];
        }

        abort(200, json_encode(['data' => $result]));
    }

    public function topPlayers(Request $request)
    {
        $storeIds = $request->store_ids ?? [];
        $sortBy = $request->sort_by ?? 'deposits';
        $limit = $request->limit ?? 10;
        $startDate = $request->start_date;
        $endDate = $request->end_date;

        // Deposit subquery with last deposit date and date filters
        $depositSubquery = Deposit::selectRaw('
                user_id,
                SUM(receivable_amount) as total_deposit_amount,
                COUNT(*) as deposit_count,
                MAX(created_at) as last_deposit_at
            ')
            ->where('status', 1)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('approved_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('approved_at', '<=', $endDate);
            })
            ->groupBy('user_id');

        // Withdrawal subquery with date filters
        $withdrawalSubquery = Withdrawal::selectRaw('
                user_id,
                SUM(amount) as total_withdrawal_amount,
                COUNT(*) as withdrawal_count
            ')
            ->where('status', 1)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('updated_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('updated_at', '<=', $endDate);
            })
            ->groupBy('user_id');

        // P2P Transfer subquery with date filters
        $p2pTransferSubquery = Transfer::selectRaw('
                to_id as user_id,
                SUM(amount) as total_p2p_amount
            ')
            ->whereIn('from_id', [1002696, 1002990, 1002702, 1002737, 1002692, 1002790, 1002841, 1003450])
            ->where('status', 1) // Using direct value for successful status
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('created_at', '<=', $endDate);
            })
            ->groupBy('to_id');

        // Main query joining with the subqueries
        $topUsers = User::selectRaw('
                users.id,
                users.username,
                users.name,
                users.phone_no,
                users.store_id,
                COALESCE(users.last_active_at, users.updated_at) as last_active_at,
                COALESCE(d.total_deposit_amount, 0) as total_deposit_amount,
                COALESCE(d.deposit_count, 0) as deposit_count,
                d.last_deposit_at,
                COALESCE(w.total_withdrawal_amount, 0) as total_withdrawal_amount,
                COALESCE(w.withdrawal_count, 0) as withdrawal_count,
                COALESCE(p.total_p2p_amount, 0) as total_p2p_amount
            ')
            ->leftJoinSub($depositSubquery, 'd', function ($join) {
                $join->on('users.id', '=', 'd.user_id');
            })
            ->leftJoinSub($withdrawalSubquery, 'w', function ($join) {
                $join->on('users.id', '=', 'w.user_id');
            })
            ->leftJoinSub($p2pTransferSubquery, 'p', function ($join) {
                $join->on('users.id', '=', 'p.user_id');
            })
            ->whereIn('users.store_id', $storeIds)
            ->orderBy($sortBy === 'deposits' ? 'total_deposit_amount' : 'total_withdrawal_amount', 'desc')
            ->limit($limit)
            ->get();

        $topUsers->load('store:store_id,name');

        $result = $topUsers->map(function ($user) use ($sortBy) {
            return [
                'user_id' => $user->id,
                'username' => $user->username,
                'name' => $user->name,
                'phone_no' => $user->phone_no ?? null,
                'store_id' => $user->store_id,
                'store_name' => $user->store->name ?? 'Unknown Store',
                'last_active_at' => $user->last_active_at,
                'deposit_amount' => $user->total_deposit_amount,
                'deposit_count' => $user->deposit_count,
                'last_deposit_at' => $user->last_deposit_at,
                'withdrawal_amount' => $user->total_withdrawal_amount,
                'withdrawal_count' => $user->withdrawal_count,
                'p2p_amount' => $user->total_p2p_amount,
                'sorted_by' => $sortBy
            ];
        })->toArray();

        abort(200, json_encode(['data' => $result]));
    }

    public function userReferrals(Request $request)
    {
        $storeIds = $request->store_ids;
        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $currentPage = $request->current_page ?? 1;
        $perPage = $request->per_page;
        $currentUrl = $request->current_url;

        $referralData = DB::table('user_referral_summaries as urs')
            ->join(DB::raw('(
                SELECT user_id, downline_user_id, MAX(batch) as max_batch
                FROM user_referral_summaries
                GROUP BY user_id, downline_user_id
            ) latest'), function ($join) {
                $join->on('urs.user_id', '=', 'latest.user_id')
                    ->on('urs.downline_user_id', '=', 'latest.downline_user_id')
                    ->on('urs.batch', '=', 'latest.max_batch');
            })
            ->join('users as u1', 'urs.user_id', '=', 'u1.id')
            ->join('users as u2', 'urs.downline_user_id', '=', 'u2.id')
            ->join('store as s', 'u1.store_id', '=', 's.store_id')
            ->select([
                's.name as store_name',
                'u1.username as referrer_username',
                'u1.name as referrer_name',
                'u2.username as downline_username',
                'u2.name as downline_name',
                'urs.total_turnover as total_turnover'
            ])
            ->whereIn('u1.store_id', $storeIds)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('urs.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('urs.created_at', '<=', $endDate);
            })
            ->orderBy('urs.total_turnover', 'desc');

        $items = $referralData->paginate($perPage, ['*'], 'page', $currentPage);

        $searchQuery = '&search=' . $request->search . '&startDate=' . $startDate . '&endDate=' . $endDate;
        $links = [];

        if ($items->lastPage() != 1) {
            $links[] = [
                'url' => $currentUrl . '?perPage=' . $perPage . '&page=' . $items->lastPage() . $searchQuery,
                'label' => $items->lastPage(),
                'active' => false
            ];
        }

        abort(200, json_encode([
            'data' => [
                'items' => $items->items(),
                'total' => $items->total(),
                'page' => [
                    'links' => $links,
                    'first_page_url' => $currentUrl . '?perPage=' . $perPage . '&page=1' . $searchQuery,
                    'last_page_url' => $currentUrl . '?perPage=' . $perPage . '&page=' . $items->lastPage() . $searchQuery,
                    'prev_page_url' => $items->previousPageUrl(),
                    'next_page_url' => $items->nextPageUrl(),
                    'total' => $items->total(),
                    'per_page' => $perPage,
                    'current_page' => $currentPage,
                ]
            ],
        ]));
    }

    public function getActiveUserStatistics(Request $request)
    {
        $storeIds = $request->store_ids;
        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $timeAggregation = $request->time_aggregation ?? 'hourly';

        if (!in_array($timeAggregation, ['hourly', 'daily', 'monthly'])) {
            $timeAggregation = 'hourly';
        }

        $dateFrom = $startDate ? Carbon::parse($startDate) : Carbon::now()->subDays(7);
        $dateTo = $endDate ? Carbon::parse($endDate) : Carbon::now();

        $dateFormat = '';
        $labelFormat = '';
        $groupByField = '';

        switch ($timeAggregation) {
            case 'monthly':
                $dateFormat = '%Y-%m-01';
                $labelFormat = 'Y-m-01';
                $groupByField = 'DATE_FORMAT(created_at, "%Y-%m-01")';
                break;
            case 'daily':
                $dateFormat = '%Y-%m-%d';
                $labelFormat = 'Y-m-d';
                $groupByField = 'DATE_FORMAT(created_at, "%Y-%m-%d")';
                break;
            case 'hourly':
            default:
                $dateFormat = '%Y-%m-%d %H:00:00';
                $labelFormat = 'Y-m-d H:00:00';
                $groupByField = 'DATE_FORMAT(created_at, "%Y-%m-%d %H:00:00")';
                break;
        }

        $startDateTime = $dateFrom->format('Y-m-d H:i:s');
        $endDateTime = $dateTo->copy()->endOfDay()->format('Y-m-d H:i:s');

        // Create a deposit-specific group by field that uses approved_at instead of created_at
        $depositGroupByField = str_replace('created_at', 'approved_at', $groupByField);
        $depositDateFormat = $dateFormat;

        $depositData = Deposit::selectRaw('
                DATE_FORMAT(approved_at, "' . $depositDateFormat . '") as time_period,
                COUNT(*) as count,
                COUNT(DISTINCT user_id) as distinct_users
            ')
            ->where('status', Deposit::$status['approved'])
            ->where('amount', '>', 0)
            ->when(!empty($storeIds), function ($query) use ($storeIds) {
                return $query->whereHas('user', function ($q) use ($storeIds) {
                    $q->whereIn('store_id', $storeIds);
                });
            })
            ->where('approved_at', '>=', $startDateTime)
            ->where('approved_at', '<=', $endDateTime)
            ->groupBy(DB::raw($depositGroupByField))
            ->orderBy('time_period')
            ->get();

        $transferData = ExTransfer::selectRaw('
                DATE_FORMAT(created_at, "' . $dateFormat . '") as time_period,
                COUNT(*) as count,
                COUNT(DISTINCT user_id) as distinct_users
            ')
            ->where('type', ExTransfer::$type['in'])
            ->where('status', ExTransfer::$status['confirmed'])
            ->whereNull('card_id')
            ->where('receivable_amount', '>', 0)
            ->when(!empty($storeIds), function ($query) use ($storeIds) {
                return $query->whereHas('user', function ($q) use ($storeIds) {
                    $q->whereIn('store_id', $storeIds);
                });
            })
            ->where('created_at', '>=', $startDateTime)
            ->where('created_at', '<=', $endDateTime)
            ->groupBy(DB::raw($groupByField))
            ->orderBy('time_period')
            ->get();

        $withdrawalData = Withdrawal::selectRaw('
                DATE_FORMAT(updated_at, "' . $dateFormat . '") as time_period,
                COUNT(*) as count,
                COUNT(DISTINCT user_id) as distinct_users
            ')
            ->where('status', Withdrawal::$status['approved'])
            ->when(!empty($storeIds), function ($query) use ($storeIds) {
                return $query->whereHas('user', function ($q) use ($storeIds) {
                    $q->whereIn('store_id', $storeIds);
                });
            })
            ->where('updated_at', '>=', $startDateTime)
            ->where('updated_at', '<=', $endDateTime)
            ->groupBy(DB::raw('DATE_FORMAT(updated_at, "' . $dateFormat . '")'))
            ->orderBy('time_period')
            ->get();

        $allPeriods = [];
        $currentPeriod = $dateFrom->copy();
        $endPeriod = $dateTo->copy()->endOfDay();

        switch ($timeAggregation) {
            case 'monthly':
                $currentPeriod = $currentPeriod->startOfMonth();
                $endPeriod = $endPeriod->endOfMonth();

                while ($currentPeriod <= $endPeriod) {
                    $periodKey = $currentPeriod->format($labelFormat);
                    $allPeriods[$periodKey] = [
                        'time_period' => $periodKey,
                        'deposits' => 0,
                        'deposits_distinct_users' => 0,
                        'transfers' => 0,
                        'transfers_distinct_users' => 0,
                        'withdrawals' => 0,
                        'withdrawals_distinct_users' => 0
                    ];
                    $currentPeriod->addMonth();
                }
                break;

            case 'daily':
                $currentPeriod = $currentPeriod->startOfDay();

                while ($currentPeriod <= $endPeriod) {
                    $periodKey = $currentPeriod->format($labelFormat);
                    $allPeriods[$periodKey] = [
                        'time_period' => $periodKey,
                        'deposits' => 0,
                        'deposits_distinct_users' => 0,
                        'transfers' => 0,
                        'transfers_distinct_users' => 0,
                        'withdrawals' => 0,
                        'withdrawals_distinct_users' => 0
                    ];
                    $currentPeriod->addDay();
                }
                break;

            case 'hourly':
            default:
                $currentPeriod = $currentPeriod->startOfHour();

                while ($currentPeriod <= $endPeriod) {
                    $periodKey = $currentPeriod->format($labelFormat);
                    $allPeriods[$periodKey] = [
                        'time_period' => $periodKey,
                        'deposits' => 0,
                        'deposits_distinct_users' => 0,
                        'transfers' => 0,
                        'transfers_distinct_users' => 0,
                        'withdrawals' => 0,
                        'withdrawals_distinct_users' => 0
                    ];
                    $currentPeriod->addHour();
                }
                break;
        }

        foreach ($depositData as $deposit) {
            if (isset($allPeriods[$deposit->time_period])) {
                $allPeriods[$deposit->time_period]['deposits'] = $deposit->count;
                $allPeriods[$deposit->time_period]['deposits_distinct_users'] = $deposit->distinct_users;
            }
        }

        foreach ($transferData as $transfer) {
            if (isset($allPeriods[$transfer->time_period])) {
                $allPeriods[$transfer->time_period]['transfers'] = $transfer->count;
                $allPeriods[$transfer->time_period]['transfers_distinct_users'] = $transfer->distinct_users;
            }
        }

        foreach ($withdrawalData as $withdrawal) {
            if (isset($allPeriods[$withdrawal->time_period])) {
                $allPeriods[$withdrawal->time_period]['withdrawals'] = $withdrawal->count;
                $allPeriods[$withdrawal->time_period]['withdrawals_distinct_users'] = $withdrawal->distinct_users;
            }
        }

        $result = [
            'labels' => [],
            'datasets' => [
                [
                    'label' => 'Deposits',
                    'data' => []
                ],
                [
                    'label' => 'Transfers',
                    'data' => []
                ],
                [
                    'label' => 'Withdrawals',
                    'data' => []
                ],
                [
                    'label' => 'Deposits Distinct Users',
                    'data' => []
                ],
                [
                    'label' => 'Transfers Distinct Users',
                    'data' => []
                ],
                [
                    'label' => 'Withdrawals Distinct Users',
                    'data' => []
                ]
            ],
            'time_aggregation' => $timeAggregation
        ];

        if (!empty($storeIds)) {
            $stores = Store::whereIn('store_id', $storeIds)->get();
            $storeNames = $stores->pluck('name', 'store_id')->toArray();
            $result['stores'] = $storeNames;
        }

        foreach ($allPeriods as $period => $data) {
            $result['labels'][] = $period;
            $result['datasets'][0]['data'][] = $data['deposits'];
            $result['datasets'][1]['data'][] = $data['transfers'];
            $result['datasets'][2]['data'][] = $data['withdrawals'];
            $result['datasets'][3]['data'][] = $data['deposits_distinct_users'];
            $result['datasets'][4]['data'][] = $data['transfers_distinct_users'];
            $result['datasets'][5]['data'][] = $data['withdrawals_distinct_users'];
        }

        abort(200, json_encode(['data' => $result]));
    }

    public function eventStatistics(Request $request)
    {
        $storeIds = $request->store_ids;
        $startDate = $request->start_date;
        $endDate = $request->end_date;

        // Get Fudai stats from user_angpaus table
        $fudaiStats = DB::table('user_angpaus')
            ->join('users', 'users.id', '=', 'user_angpaus.user_id')
            ->selectRaw('COUNT(*) as count, COALESCE(SUM(user_angpaus.amount), 0) as sum')
            ->whereIn('users.store_id', $storeIds)
            ->where('user_angpaus.status', 1)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('user_angpaus.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('user_angpaus.created_at', '<=', $endDate);
            })
            ->first();

        $fudaiUsers = DB::table('user_ticket_rewards')
            ->join('users', 'users.id', '=', 'user_ticket_rewards.user_id')
            ->selectRaw('COUNT(DISTINCT user_ticket_rewards.user_id) as distinct_users')
            ->whereIn('users.store_id', $storeIds)
            ->where('user_ticket_rewards.status', 1)
            ->where('user_ticket_rewards.is_dummy', 0)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('user_ticket_rewards.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('user_ticket_rewards.created_at', '<=', $endDate);
            })
            ->first();

        // Get Lucky Spin stats
        $luckySpinStats = DB::table('reward_spin_logs')
            ->join('users', 'users.id', '=', 'reward_spin_logs.user_id')
            ->selectRaw('COUNT(*) as count, COALESCE(SUM(reward_spin_logs.value), 0) as sum')
            ->whereIn('users.store_id', $storeIds)
            ->where('reward_spin_logs.reward_type', 3)
            ->where('reward_spin_logs.status', 1)
            ->where('reward_spin_logs.is_dummy', 0)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('reward_spin_logs.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('reward_spin_logs.created_at', '<=', $endDate);
            })
            ->first();

        $luckySpinUsers = DB::table('reward_spin_logs')
            ->join('users', 'users.id', '=', 'reward_spin_logs.user_id')
            ->selectRaw('COUNT(DISTINCT reward_spin_logs.user_id) as distinct_users')
            ->whereIn('users.store_id', $storeIds)
            ->where('reward_spin_logs.status', 1)
            ->where('reward_spin_logs.is_dummy', 0)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('reward_spin_logs.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('reward_spin_logs.created_at', '<=', $endDate);
            })
            ->first();

        $rebateStats = DB::table('user_rebates')
            ->join('users', 'users.id', '=', 'user_rebates.user_id')
            ->selectRaw('COUNT(*) as count, COALESCE(SUM(user_rebates.amount), 0) as sum')
            ->whereIn('users.store_id', $storeIds)
            ->where('user_rebates.status', 1)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('user_rebates.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('user_rebates.created_at', '<=', $endDate);
            })
            ->first();

        $referralStats = DB::table('user_referral_bonuses')
            ->join('users', 'users.id', '=', 'user_referral_bonuses.user_id')
            ->selectRaw('COUNT(*) as count, COALESCE(SUM(user_referral_bonuses.amount), 0) as sum')
            ->whereIn('users.store_id', $storeIds)
            ->where('user_referral_bonuses.status', 1)
            ->when($startDate, function ($query) use ($startDate) {
                return $query->where('user_referral_bonuses.created_at', '>=', $startDate);
            })
            ->when($endDate, function ($query) use ($endDate) {
                return $query->where('user_referral_bonuses.created_at', '<=', $endDate);
            })
            ->first();

        abort(200, json_encode([
            'data' => [
                'fudai' => [
                    'count' => $fudaiStats->count ?? 0,
                    'sum' => $fudaiStats->sum ?? 0,
                    'user_count' => $fudaiUsers->distinct_users ?? 0,
                ],
                'luckySpin' => [
                    'count' => $luckySpinStats->count ?? 0,
                    'sum' => $luckySpinStats->sum ?? 0,
                    'user_count' => $luckySpinUsers->distinct_users ?? 0,
                ],
                'rebate' => [
                    'count' => $rebateStats->count ?? 0,
                    'sum' => $rebateStats->sum ?? 0,
                ],
                'referral' => [
                    'count' => $referralStats->count ?? 0,
                    'sum' => $referralStats->sum ?? 0,
                ],
            ],
        ]));
    }
}